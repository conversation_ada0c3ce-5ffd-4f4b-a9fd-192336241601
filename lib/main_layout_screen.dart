import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/presentation/screens/reports/report_screen.dart';
import '/src/presentation/shared/components/breadcrumb_navigation.dart';
import '/src/presentation/shared/components/header.dart';
import '/src/presentation/screens/agent/components/agents_table.dart';
import '/src/presentation/screens/sales/sales_review_doc_screen.dart';
import '/src/presentation/screens/dashboard/components/mobile_drawer.dart';

import 'src/core/config/app_strings.dart';
import 'src/core/config/constants.dart';
import 'src/core/config/responsive.dart';
import 'src/core/theme/app_theme.dart';
import 'src/core/enum/user_role.dart';
import 'src/domain/models/user.dart';
import 'src/presentation/screens/broker/register_broker_screen.dart';
import 'src/presentation/screens/dashboard/components/dashboard_content.dart';
import 'src/presentation/screens/dashboard/dashboard_screen.dart';

class MainLayoutScreen extends HookWidget {
  MainLayoutScreen({super.key});

  // TODO: Remove after API integration
  final User user = User(
    name: "Na<PERSON>",
    email: "<EMAIL>",
    phone: "9876543210",
    image: "$iconAssetpath/agent_round.png",
    role: UserRole.platformOwner,
  );

  @override
  Widget build(BuildContext context) {
    final selectedTabIndex = useState<int>(0);

    final tabs = [
      {
        'title': dashboardTab,
        'content': _buildDashboardContent(),
        'icon': Icons.dashboard_outlined,
      },
      {
        'title': brokersTab,
        'content': const Center(child: Text('Brokers Content')),
        'icon': Icons.business_outlined,
      },
      {
        'title': agentsTab,
        'content': _buildAgentsContent(),
        'icon': Icons.people_outline,
      },
      {
        'title': salesTab,
        'content': _buildSalesContent(),
        'icon': Icons.trending_up_outlined,
      },
      {
        'title': commissionTab,
        'content': const Center(child: Text('Commission Content')),
        'icon': Icons.account_balance_wallet_outlined,
      },
      {
        'title': reportsTab,
        'content': _buildReportsContent(),
        // const Center(child: Text('Reports Content')),
        'icon': Icons.assessment_outlined,
      },
      // Hidden tab for Register Broker - not shown in navigation
      {
        'title': 'Register Broker',
        'content': _buildRegisterBrokerContent(),
        'icon': Icons.add,
        'hidden': true, // This tab won't appear in navigation
      },
      {
        'title': 'Agent Network Screen',
        'content': _buildAgentNetwork(),
        'icon': Icons.add,
        'hidden': true, // This tab won't appear in navigation
      },
    ];

    void onTabSelected(int index) {
      selectedTabIndex.value = index;
    }

    void onAddNewPressed() {
      // Find the index of the "Register Broker" tab (the hidden one)
      final registerBrokerIndex = tabs.indexWhere(
        (tab) => tab['title'] == 'Register Broker',
      );
      if (registerBrokerIndex != -1) {
        selectedTabIndex.value = registerBrokerIndex;
      }
    }

    final bool isSmallMobile = Responsive.isSmallMobile(context);

    return Scaffold(
      backgroundColor: AppTheme.scaffoldBgColor,
      drawer: Responsive.showDrawer(context)
          ? MobileDrawer(
              user: user,
              selectedTab: ValueNotifier(
                tabs[selectedTabIndex.value]['title'] as String,
              ),
              selectedTabIndex: selectedTabIndex.value,
              onTabSelected: onTabSelected,
              tabs: tabs,
              onAddNewPressed: onAddNewPressed,
            )
          : null,
      body: SafeArea(
        child: Column(
          children: [
            // Fixed Header at the top
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: isSmallMobile ? 12 : webLayoutmargin,
              ),
              child: Header(
                tabs: tabs,
                selectedTabIndex: selectedTabIndex.value,
                onTabSelected: onTabSelected,
                user: user,
                onAddNewPressed: onAddNewPressed,
              ),
            ),

            // Scrollable content below the header
            Expanded(
              child: SingleChildScrollView(
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: isSmallMobile ? 12 : webLayoutmargin,
                  ),
                  child: Column(
                    children: [
                      const SizedBox(height: defaultPadding),

                      // Breadcrumb
                      if (selectedTabIndex.value > 0) ...[
                        BreadCrumbNavigation(
                          hierarchyPath: [
                            dashboardAdmin,
                            tabs[selectedTabIndex.value]['title'] as String,
                          ],
                          onNavigate: (int navigationIndex) {
                            // If navigating back to dashboard from Register Broker
                            if (navigationIndex == 0) {
                              selectedTabIndex.value = 0; // Go to dashboard
                            } else {
                              onTabSelected(navigationIndex);
                            }
                          },
                        ),
                        const SizedBox(height: defaultPadding),
                      ],

                      // Dynamic Content Area - Scrollable
                      AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        transitionBuilder:
                            (Widget child, Animation<double> animation) {
                              final fadeAnimation =
                                  Tween<double>(begin: 0.0, end: 1.0).animate(
                                    CurvedAnimation(
                                      parent: animation,
                                      curve: Curves
                                          .easeInOut, // You can use any curve
                                    ),
                                  );
                              return FadeTransition(
                                opacity: fadeAnimation,
                                child: child,
                              );
                            },
                        child: Container(
                          key: ValueKey(selectedTabIndex.value),
                          child:
                              tabs[selectedTabIndex.value]['content'] as Widget,
                        ),
                      ),

                      // Footer
                      const Footer(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDashboardContent() {
    return DashboardScreen();
  }

  Widget _buildAgentsContent() {
    return Builder(
      builder: (context) {
        return const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AgentsTable(),
            SizedBox(height: defaultPadding),
          ],
        );
      },
    );
  }

  Widget _buildSalesContent() {
    return Builder(
      builder: (context) {
        return Container(
          padding: EdgeInsets.symmetric(
            horizontal: Responsive.isMobile(context) ? 8 : webLayoutmargin,
            vertical: Responsive.isMobile(context) ? 8 : defaultMargin,
          ),
          child: SalesReviewDocScreen(enableEditing: false),
        );
      },
    );
  }

  Widget _buildRegisterBrokerContent() {
    return Builder(
      builder: (context) {
        return RegisterBrokerScreen();
      },
    );
  }

  Widget _buildAgentNetwork() {
    return Container();
    // return Builder(
    //   builder: (context) {
    //     return AgentNetworkScreen(selectedBroker: null,);
    //   },
    // );
  }

  Widget _buildReportsContent() {
    return Builder(
      builder: (context) {
        return SizedBox(
          height: MediaQuery.of(context).size.height - 200, // Give it a bounded height
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Responsive.isMobile(context) ? 8 : webLayoutmargin,
              vertical: Responsive.isMobile(context) ? 8 : defaultMargin,
            ),
            child: const ReportScreen(),
          ),
        );
      },
    );
  }

}
